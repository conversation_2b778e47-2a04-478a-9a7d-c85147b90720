namespace Application.Services.Mapper;

/// <summary>
/// Rozhraní pro mapování z entity na DTO (pouze čtení).
/// </summary>
/// <typeparam name="TSource">Zdrojový typ entity, ze kterého se mapuje.</typeparam>
/// <typeparam name="TTarget">Cílový typ DTO, na který se mapuje.</typeparam>
public interface IMapper<in TSource, out TTarget>
    where TSource : class
    where TTarget : class, new()
{
    /// <summary>
    /// Mapuje zdrojový objekt na cílový objekt pomocí automatického párování názvů vlastností.
    /// </summary>
    /// <param name="source">Zdrojový objekt, ze kterého se mapuje.</param>
    /// <returns>Nová instance cílového typu s vlastnostmi automaticky namapovanými ze zdroje.</returns>
    TTarget MapDirect(TSource source);

    /// <summary>
    /// Mapuje kolekci zdrojových objektů na cílové objekty pomocí automatického párování názvů vlastností.
    /// </summary>
    /// <param name="sources">Kolekce zdrojových objektů, ze kterých se mapuje.</param>
    /// <returns>Kolekce cílových objektů automaticky namapovaných ze zdrojové kolekce.</returns>
    IEnumerable<TTarget> MapDirectCollection(IEnumerable<TSource> sources);
}

/// <summary>
/// Rozhraní pro obousměrné mapování mezi DTO a entitou.
/// </summary>
/// <typeparam name="TDto">Typ DTO.</typeparam>
/// <typeparam name="TEntity">Typ entity.</typeparam>
public interface IBidirectionalMapper<TDto, TEntity>
    where TDto : class, new()
    where TEntity : class
{
    /// <summary>
    /// Mapuje DTO na novou instanci entity.
    /// </summary>
    /// <param name="dto">DTO objekt, ze kterého se mapuje.</param>
    /// <returns>Nová instance entity s vlastnostmi namapovanými z DTO.</returns>
    TEntity MapDirect(TDto dto);

    /// <summary>
    /// Mapuje DTO na existující instanci entity (aktualizace).
    /// </summary>
    /// <param name="dto">DTO objekt, ze kterého se mapuje.</param>
    /// <param name="entity">Existující instance entity, která se aktualizuje.</param>
    void Map(TDto dto, TEntity entity);

    /// <summary>
    /// Mapuje entitu na DTO.
    /// </summary>
    /// <param name="entity">Entity objekt, ze kterého se mapuje.</param>
    /// <returns>Nová instance DTO s vlastnostmi namapovanými z entity.</returns>
    TDto MapDirect(TEntity entity);

    /// <summary>
    /// Mapuje kolekci entit na kolekci DTO.
    /// </summary>
    /// <param name="entities">Kolekce entit, ze kterých se mapuje.</param>
    /// <returns>Kolekce DTO objektů namapovaných z entit.</returns>
    IEnumerable<TDto> MapDirectCollection(IEnumerable<TEntity> entities);
}