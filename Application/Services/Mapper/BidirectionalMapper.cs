using System.Reflection;
using System.Linq.Expressions;

namespace Application.Services.Mapper;

/// <summary>
/// Implementace obousměrného mapperu pro mapování mezi DTO a entitou.
/// </summary>
/// <typeparam name="TDto">Typ DTO.</typeparam>
/// <typeparam name="TEntity">Typ entity.</typeparam>
public class BidirectionalMapper<TDto, TEntity> : IBidirectionalMapper<TDto, TEntity>
    where TDto : class, new()
    where TEntity : class
{
    /// <summary>
    /// Líně načítaná funkce pro mapování z DTO na entitu.
    /// </summary>
    private static readonly Lazy<Func<TDto, TEntity>> _dtoToEntityMapperLazy = new(
        BuildDtoToEntityMapper,
        LazyThreadSafetyMode.ExecutionAndPublication
    );

    /// <summary>
    /// Líně načítaná funkce pro mapování z entity na DTO.
    /// </summary>
    private static readonly Lazy<Func<TEntity, TDto>> _entityToDtoMapperLazy = new(
        BuildEntityToDtoMapper,
        LazyThreadSafetyMode.ExecutionAndPublication
    );

    /// <summary>
    /// Líně načítaná akce pro aktualizaci entity z DTO.
    /// </summary>
    private static readonly Lazy<Action<TDto, TEntity>> _updateEntityMapperLazy = new(
        BuildUpdateEntityMapper,
        LazyThreadSafetyMode.ExecutionAndPublication
    );

    /// <summary>
    /// Mapuje DTO na novou instanci entity.
    /// </summary>
    /// <param name="dto">DTO objekt, ze kterého se mapuje.</param>
    /// <returns>Nová instance entity s vlastnostmi namapovanými z DTO.</returns>
    /// <exception cref="ArgumentNullException">Vyvoláno, když je DTO null.</exception>
    public TEntity MapDirect(TDto dto)
    {
        if (dto == null) throw new ArgumentNullException(nameof(dto));
        return _dtoToEntityMapperLazy.Value(dto);
    }

    /// <summary>
    /// Mapuje DTO na existující instanci entity (aktualizace).
    /// </summary>
    /// <param name="dto">DTO objekt, ze kterého se mapuje.</param>
    /// <param name="entity">Existující instance entity, která se aktualizuje.</param>
    /// <exception cref="ArgumentNullException">Vyvoláno, když je DTO nebo entita null.</exception>
    public void Map(TDto dto, TEntity entity)
    {
        if (dto == null) throw new ArgumentNullException(nameof(dto));
        if (entity == null) throw new ArgumentNullException(nameof(entity));
        _updateEntityMapperLazy.Value(dto, entity);
    }

    /// <summary>
    /// Mapuje entitu na DTO.
    /// </summary>
    /// <param name="entity">Entity objekt, ze kterého se mapuje.</param>
    /// <returns>Nová instance DTO s vlastnostmi namapovanými z entity.</returns>
    /// <exception cref="ArgumentNullException">Vyvoláno, když je entita null.</exception>
    public TDto MapDirect(TEntity entity)
    {
        if (entity == null) throw new ArgumentNullException(nameof(entity));
        return _entityToDtoMapperLazy.Value(entity);
    }

    /// <summary>
    /// Mapuje kolekci entit na kolekci DTO.
    /// </summary>
    /// <param name="entities">Kolekce entit, ze kterých se mapuje.</param>
    /// <returns>Kolekce DTO objektů namapovaných z entit.</returns>
    /// <exception cref="ArgumentNullException">Vyvoláno, když je kolekce entit null.</exception>
    public IEnumerable<TDto> MapDirectCollection(IEnumerable<TEntity> entities)
    {
        if (entities == null) throw new ArgumentNullException(nameof(entities));
        return entities.Select(_entityToDtoMapperLazy.Value);
    }

    /// <summary>
    /// Vytváří funkci pro mapování z DTO na entitu.
    /// </summary>
    /// <returns>Funkce, která mapuje DTO na entitu.</returns>
    private static Func<TDto, TEntity> BuildDtoToEntityMapper()
    {
        var dtoParam = Expression.Parameter(typeof(TDto), "dto");
        var entity = Expression.Variable(typeof(TEntity), "entity");

        var assignments = new List<Expression>();

        // Pokusíme se vytvořit instanci entity
        // Pokud má entity konstruktor bez parametrů, použijeme ho
        var entityConstructor = typeof(TEntity).GetConstructor(Type.EmptyTypes);
        if (entityConstructor != null)
        {
            assignments.Add(Expression.Assign(entity, Expression.New(entityConstructor)));
        }
        else
        {
            // Pokud nemá konstruktor bez parametrů, použijeme Activator.CreateInstance
            var createInstanceMethod = typeof(Activator).GetMethod(nameof(Activator.CreateInstance), new[] { typeof(Type) });
            var createInstanceCall = Expression.Call(createInstanceMethod!, Expression.Constant(typeof(TEntity)));
            assignments.Add(Expression.Assign(entity, Expression.Convert(createInstanceCall, typeof(TEntity))));
        }

        // Mapování vlastností
        foreach (var entityProp in typeof(TEntity).GetProperties())
        {
            if (!entityProp.CanWrite) continue;

            var dtoProp = typeof(TDto).GetProperty(
                entityProp.Name,
                BindingFlags.Public | BindingFlags.Instance
            );

            if (dtoProp == null || !dtoProp.CanRead) continue;

            var dtoValue = Expression.Property(dtoParam, dtoProp);
            var convertedValue = entityProp.PropertyType != dtoProp.PropertyType
                ? Expression.Convert(dtoValue, entityProp.PropertyType)
                : (Expression)dtoValue;

            var assignExpr = Expression.Call(entity, entityProp.SetMethod!, convertedValue);
            assignments.Add(assignExpr);
        }

        // Přidáme návrat hodnoty na konec
        assignments.Add(entity);

        var body = Expression.Block(
            new[] { entity },
            assignments
        );

        return Expression.Lambda<Func<TDto, TEntity>>(body, dtoParam).Compile();
    }

    /// <summary>
    /// Vytváří funkci pro mapování z entity na DTO.
    /// </summary>
    /// <returns>Funkce, která mapuje entitu na DTO.</returns>
    private static Func<TEntity, TDto> BuildEntityToDtoMapper()
    {
        var entityParam = Expression.Parameter(typeof(TEntity), "entity");
        var dto = Expression.Variable(typeof(TDto), "dto");

        var assignments = new List<Expression>
        {
            Expression.Assign(dto, Expression.New(typeof(TDto)))
        };

        foreach (var dtoProp in typeof(TDto).GetProperties())
        {
            if (!dtoProp.CanWrite) continue;

            var entityProp = typeof(TEntity).GetProperty(
                dtoProp.Name,
                BindingFlags.Public | BindingFlags.Instance
            );

            if (entityProp == null || !entityProp.CanRead) continue;

            var entityValue = Expression.Property(entityParam, entityProp);
            var convertedValue = dtoProp.PropertyType != entityProp.PropertyType
                ? Expression.Convert(entityValue, dtoProp.PropertyType)
                : (Expression)entityValue;

            var assignExpr = Expression.Call(dto, dtoProp.SetMethod!, convertedValue);
            assignments.Add(assignExpr);
        }

        // Přidáme návrat hodnoty na konec
        assignments.Add(dto);

        var body = Expression.Block(
            new[] { dto },
            assignments
        );

        return Expression.Lambda<Func<TEntity, TDto>>(body, entityParam).Compile();
    }

    /// <summary>
    /// Vytváří akci pro aktualizaci entity z DTO.
    /// </summary>
    /// <returns>Akce, která aktualizuje entitu z DTO.</returns>
    private static Action<TDto, TEntity> BuildUpdateEntityMapper()
    {
        var dtoParam = Expression.Parameter(typeof(TDto), "dto");
        var entityParam = Expression.Parameter(typeof(TEntity), "entity");

        var assignments = new List<Expression>();

        foreach (var entityProp in typeof(TEntity).GetProperties())
        {
            if (!entityProp.CanWrite) continue;

            var dtoProp = typeof(TDto).GetProperty(
                entityProp.Name,
                BindingFlags.Public | BindingFlags.Instance
            );

            if (dtoProp == null || !dtoProp.CanRead) continue;

            var dtoValue = Expression.Property(dtoParam, dtoProp);
            var convertedValue = entityProp.PropertyType != dtoProp.PropertyType
                ? Expression.Convert(dtoValue, entityProp.PropertyType)
                : (Expression)dtoValue;

            var assignExpr = Expression.Call(entityParam, entityProp.SetMethod!, convertedValue);
            assignments.Add(assignExpr);
        }

        var body = Expression.Block(assignments);

        return Expression.Lambda<Action<TDto, TEntity>>(body, dtoParam, entityParam).Compile();
    }
}
